package com.trading.financialindicatordaemon.service.mining;

import com.trading.financialindicatordaemon.mapper.MiningSymbol;
import com.trading.financialindicatordaemon.mapper.MiningSymbolMapper;
import org.springframework.stereotype.Component;

import java.time.LocalDate;
import java.util.List;

@Component
public class MiningSymbolService {

    private final MiningSymbolMapper miningSymbolMapper;

    public MiningSymbolService(MiningSymbolMapper miningSymbolMapper) {
        this.miningSymbolMapper = miningSymbolMapper;
    }

    public void updateCryptoLastMinedDate(String symbol) {
        miningSymbolMapper.updateLastMinedDate(symbol, LocalDate.now());
    }

    public void updateCryptoLastMinedDate(String symbol) {
        miningSymbolMapper.updateCryptoLastMinedDate(symbol, LocalDate.now());
    }

    public void updateStockLastMinedDate(String symbol) {
        miningSymbolMapper.updateStockLastMinedDate(symbol, LocalDate.now(), "CRYPTO");
    }

    public List<MiningSymbol> findActiveCryptoSymbols() {
        return miningSymbolMapper.findActiveSymbols("CRYPTO");
    }

    public List<MiningSymbol> findActiveCryptoSymbols() {
        return miningSymbolMapper.findActiveCryptoSymbols();
    }

    public List<MiningSymbol> findActiveStockSymbols() {
        return miningSymbolMapper.findActiveStockSymbols();
    }

}
