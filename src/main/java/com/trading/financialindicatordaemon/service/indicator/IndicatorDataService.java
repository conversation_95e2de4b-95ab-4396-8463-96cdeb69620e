package com.trading.financialindicatordaemon.service.indicator;

import com.trading.financialindicatordaemon.client.cmc.CryptoCandleHistoricalQuote;
import com.trading.financialindicatordaemon.client.indicatorapi.CalculateIndicatorsRequest;
import com.trading.financialindicatordaemon.client.indicatorapi.IndicatorApiClient;
import com.trading.financialindicatordaemon.client.indicatorapi.IndicatorData;
import com.trading.financialindicatordaemon.mapper.IndicatorDataMapper;
import com.trading.financialindicatordaemon.mapper.IndicatorDataWrapper;
import com.trading.financialindicatordaemon.service.cmc.CmcCandleDataService;
import com.trading.financialindicatordaemon.service.stock.StockCandleData;
import com.trading.financialindicatordaemon.service.stock.StockCandleDataService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Service
public class IndicatorDataService {

    private static final Logger logger = LoggerFactory.getLogger(IndicatorDataService.class);

    private final IndicatorApiClient indicatorApiClient;
    private final CmcCandleDataService cmcCandleDataService;
    private final IndicatorDataMapper indicatorDataMapper;
    private final StockCandleDataService stockCandleDataService;

    public IndicatorDataService(@SuppressWarnings("SpringJavaInjectionPointsAutowiringInspection")
                                IndicatorApiClient indicatorApiClient,
                                CmcCandleDataService cmcCandleDataService,
                                IndicatorDataMapper indicatorDataMapper, StockCandleDataService stockCandleDataService) {
        this.indicatorApiClient = indicatorApiClient;
        this.cmcCandleDataService = cmcCandleDataService;
        this.indicatorDataMapper = indicatorDataMapper;
        this.stockCandleDataService = stockCandleDataService;
    }

    public void calculateForCrypto(String symbol, String conversionCurrency) {
        List<CryptoCandleHistoricalQuote> quotes = cmcCandleDataService.find(symbol, conversionCurrency);
        List<CalculateIndicatorsRequest> requests = quotes.stream()
                .sorted(Comparator.comparing(q -> q.getQuote().getTimestamp()))
                .map(
                        quote -> {
                            CalculateIndicatorsRequest request = new CalculateIndicatorsRequest();
                            request.setClose(quote.getQuote().getClose());
                            request.setHigh(quote.getQuote().getHigh());
                            request.setLow(quote.getQuote().getLow());
                            request.setMarketCap(quote.getQuote().getMarketCap());
                            request.setName(quote.getQuote().getName());
                            request.setOpen(quote.getQuote().getOpen());
                            request.setTimestamp(quote.getQuote().getTimestamp());
                            request.setVolume(quote.getQuote().getVolume());
                            return request;
                        }
                ).toList();

        Optional<IndicatorDataWrapper> latestByCoinAndCurrency =
                indicatorDataMapper.findLatestByCoinAndCurrency(symbol, conversionCurrency, "STOCK");

        if (latestByCoinAndCurrency.isPresent() && latestDataExists(latestByCoinAndCurrency.get(), requests)) {
            logger.info("Latest indicator data already exists for {}/{}", symbol, conversionCurrency);
            return;
        }

        calculate(symbol, conversionCurrency, requests);
    }

    public void calculateForStock(String symbol, String conversionCurrency) {
        List<StockCandleData> quotes = stockCandleDataService.find(symbol, conversionCurrency);
        List<CalculateIndicatorsRequest> requests = quotes.stream()
                .sorted(Comparator.comparing(StockCandleData::getTimestamp))
                .map(
                        quote -> {
                            CalculateIndicatorsRequest request = new CalculateIndicatorsRequest();
                            request.setClose(quote.getClose());
                            request.setHigh(quote.getHigh());
                            request.setLow(quote.getLow());
                            request.setOpen(quote.getOpen());
                            request.setTimestamp(quote.getTimestamp().toString());
                            request.setVolume(BigDecimal.valueOf(quote.getVolume()));
                            request.setName(quote.getSymbol());
                            return request;
                        }
                ).toList();

        calculate(symbol, conversionCurrency, requests);
    }

    private void calculate(String symbol, String conversionCurrency, List<CalculateIndicatorsRequest> requests) {
        ResponseEntity<List<IndicatorData>> response = indicatorApiClient.calculateIndicators(requests);

        if (!response.getStatusCode().is2xxSuccessful()) {
            throw new RuntimeException("Failed to calculate indicators: " + response.getStatusCode());
        }

        insert(symbol, conversionCurrency, response.getBody());
    }

    public List<IndicatorData> findForCrypto(String symbol, String conversionCurrency) {
        Optional<IndicatorDataWrapper> wrapper =
                indicatorDataMapper.findLatestByCoinAndCurrency(symbol, conversionCurrency, "CRYPTO");

        if (wrapper.isPresent()) {
            return wrapper.get().getIndicatorValues();
        }

        logger.warn("No indicator data found for symbol: {} and currency: {}", symbol, conversionCurrency);
        return List.of();
    }

    public List<IndicatorData> findForStock(String symbol, String conversionCurrency) {
        Optional<IndicatorDataWrapper> wrapper =
                indicatorDataMapper.findLatestByCoinAndCurrency(symbol, conversionCurrency, "STOCK");

        if (wrapper.isPresent()) {
            return wrapper.get().getIndicatorValues();
        }

        logger.warn("No indicator data found for symbol: {} and currency: {}", symbol, conversionCurrency);
        return List.of();
    }

    public void insert(String symbol, String conversionCurrency, List<IndicatorData> data) {
        indicatorDataMapper.insert(symbol, conversionCurrency, data);
    }

    public List<IndicatorDataWrapper> findAllCryptoWithLatestIndicatorDataOnly() {
        return indicatorDataMapper.findAllWithLatestIndicatorDataOnly("CRYPTO");
    }

    public List<IndicatorDataWrapper> findAllStockWithLatestIndicatorDataOnly() {
        return indicatorDataMapper.findAllWithLatestIndicatorDataOnly("STOCK");
    }

    private boolean latestDataExists(IndicatorDataWrapper latestByCoinAndCurrency,
                                     List<CalculateIndicatorsRequest> requests) {
        return latestByCoinAndCurrency
                .getIndicatorValues()
                .getFirst()
                .getTimestamp().equals(
                        requests
                                .getLast()
                                .getTimestamp());
    }

}
