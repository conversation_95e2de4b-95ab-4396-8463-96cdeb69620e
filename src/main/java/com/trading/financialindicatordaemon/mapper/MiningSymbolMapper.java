package com.trading.financialindicatordaemon.mapper;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDate;
import java.util.List;

@Mapper
public interface MiningSymbolMapper {

    List<MiningSymbol> findActiveSymbols(@Param("symbolType") String symbolType);

    List<MiningSymbol> findActiveCryptoSymbols();

    List<MiningSymbol> findActiveStockSymbols();

    void updateLastMinedDate(@Param("symbol") String symbol,
                             @Param("date") LocalDate date,
                             @Param("symbolType") String symbolType);

    void updateCryptoLastMinedDate(@Param("symbol") String symbol, @Param("date") LocalDate date);

    void updateStockLastMinedDate(@Param("symbol") String symbol, @Param("date") LocalDate date);

}
