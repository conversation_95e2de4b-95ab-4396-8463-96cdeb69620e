package com.trading.financialindicatordaemon.controller;

import com.trading.financialindicatordaemon.client.indicatorapi.IndicatorData;
import com.trading.financialindicatordaemon.service.indicator.IndicatorDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static org.slf4j.LoggerFactory.getLogger;

@RestController
@Tag(name = "Indicators", description = "Cryptocurrency and stock indicator data API")
public class IndicatorController {
    private static final org.slf4j.Logger logger = getLogger(IndicatorController.class);

    private final IndicatorDataService indicatorDataService;

    public IndicatorController(IndicatorDataService indicatorDataService) {
        this.indicatorDataService = indicatorDataService;
    }

    @Operation(
            summary = "Get indicator data by symbol and conversion currency",
            description = "Retrieve technical indicator data for a specific cryptocurrency symbol and conversion currency pair"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Indicator data retrieved successfully",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = IndicatorData.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content
            )
    })
    @GetMapping("/api/v1/crypto/indicators")
    public ResponseEntity<List<IndicatorData>> getIndicatorData(
            @Parameter(description = "Cryptocurrency symbol (e.g., BTC, ETH, SOL)", example = "SOL")
            @RequestParam String symbol,
            @Parameter(description = "Conversion currency (e.g., USD, BTC)", example = "USD")
            @RequestParam String conversionCurrency
    ) {
        logger.info("Fetching indicator data for symbol: {} and conversion currency: {}", symbol, conversionCurrency);

        List<IndicatorData> indicatorData = indicatorDataService.findForCrypto(symbol, conversionCurrency);

        logger.info("Found {} indicator data records for {}/{}", indicatorData.size(), symbol, conversionCurrency);

        return ResponseEntity.ok(indicatorData);
    }

    @Operation(
            summary = "Get stock indicator data by symbol and conversion currency",
            description = "Retrieve technical indicator data for a specific stock symbol and conversion currency pair"
    )
    @ApiResponses(value = {
            @ApiResponse(
                    responseCode = "200",
                    description = "Indicator data retrieved successfully",
                    content = @Content(
                            mediaType = "application/json",
                            schema = @Schema(implementation = IndicatorData.class)
                    )
            ),
            @ApiResponse(
                    responseCode = "500",
                    description = "Internal server error",
                    content = @Content
            )
    })
    @GetMapping("/api/v1/stock/indicators")
    public ResponseEntity<List<IndicatorData>> getStockIndicatorData(
            @Parameter(description = "Stock symbol (e.g., AAPL, TSLA, MSFT)", example = "AAPL")
            @RequestParam String symbol,
            @Parameter(description = "Conversion currency (e.g., USD, EUR)", example = "USD")
            @RequestParam String conversionCurrency
    ) {
        logger.info("Fetching stock indicator data for symbol: {} and conversion currency: {}", symbol, conversionCurrency);

        List<IndicatorData> indicatorData = indicatorDataService.findForStock(symbol, conversionCurrency);

        logger.info("Found {} stock indicator data records for {}/{}", indicatorData.size(), symbol, conversionCurrency);

        return ResponseEntity.ok(indicatorData);
    }

}
