package com.trading.financialindicatordaemon.service.mining;

import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.mapper.MiningSymbol;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.context.jdbc.Sql;

import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;


public class MiningSymbolServiceTest extends BaseTest {

    @Autowired
    private MiningSymbolService miningSymbolService;

    @Test
    @Sql(scripts = "classpath:db/mining_symbols.sql")
    public void findActiveSymbols_shouldReturnActiveCryptoSymbols() {
        List<MiningSymbol> activeSymbols = miningSymbolService.findActiveCryptoSymbols();
        assertThat(activeSymbols).isNotEmpty();
        assertThat(activeSymbols.stream().map(MiningSymbol::getSymbol).toList()).contains("ETH", "SOL");
        assertThat(activeSymbols).hasSize(2);
    }

    @Test
    @Sql(scripts = "classpath:db/mining_symbols.sql")
    public void updateCryptoLastMinedDate_shouldUpdate() {
        assertThat(miningSymbolService.findActiveCryptoSymbols()
                .stream()
                .map(MiningSymbol::getSymbol)
                .toList())
                .contains("ETH", "SOL");

        miningSymbolService.updateCryptoLastMinedDate("ETH");

        assertThat(miningSymbolService.findActiveCryptoSymbols()
                .stream()
                .map(MiningSymbol::getSymbol)
                .toList())
                .contains("SOL");
    }

    @Test
    @Sql(scripts = "classpath:db/stock_mining_symbols.sql")
    public void findActiveStockSymbols_shouldReturnActiveStockSymbols() {
        List<MiningSymbol> activeSymbols = miningSymbolService.findActiveStockSymbols();
        assertThat(activeSymbols).isNotEmpty();
        assertThat(activeSymbols.stream().map(MiningSymbol::getSymbol).toList()).contains("TSLA", "MSFT");
        assertThat(activeSymbols).hasSize(2);
    }

    @Test
    @Sql(scripts = "classpath:db/stock_mining_symbols.sql")
    public void updateStockLastMinedDate_shouldUpdate() {
        assertThat(miningSymbolService.findActiveStockSymbols()
                .stream()
                .map(MiningSymbol::getSymbol)
                .toList())
                .contains("TSLA", "MSFT");

        miningSymbolService.updateStockLastMinedDate("TSLA");

        assertThat(miningSymbolService.findActiveStockSymbols()
                .stream()
                .map(MiningSymbol::getSymbol)
                .toList())
                .contains("MSFT");
    }

}
