package com.trading.financialindicatordaemon.amqp.listener;

import com.rabbitmq.client.Channel;
import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.amqp.publisher.RabbitMqPublisher;
import com.trading.financialindicatordaemon.mapper.MiningSymbol;
import com.trading.financialindicatordaemon.service.mining.MiningSymbolService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.jdbc.Sql;

import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

public class MineActiveStockSymbolsListenerTest extends BaseTest {

    @Autowired
    private MineActiveStockSymbolsListener mineActiveStockSymbolsListener;

    @Autowired
    private MiningSymbolService miningSymbolService;

    @MockBean
    private RabbitMqPublisher rabbitMqPublisher;

    @MockBean
    private Channel channel;

    @Test
    @Sql(scripts = "classpath:db/stock_mining_symbols.sql")
    public void handleMineActiveStockSymbols_shouldProcessActiveSymbols() throws Exception {
        // Verify initial state - should have 2 active symbols (TSLA and MSFT)
        List<MiningSymbol> initialActiveSymbols = miningSymbolService.findActiveStockSymbols();
        assertThat(initialActiveSymbols).hasSize(2);
        assertThat(initialActiveSymbols.stream().map(MiningSymbol::getSymbol).toList())
                .contains("TSLA", "MSFT");

        // Execute the listener
        mineActiveStockSymbolsListener.handleMineActiveStockSymbols(
                new HashMap<>(), channel, 1L, null);

        // Verify channel acknowledgment
        verify(channel).basicAck(1L, false);

        // Verify publisher was called with symbols
        verify(rabbitMqPublisher).mineStockDataBySymbols(any());

        // Verify that last mined dates were updated (should reduce active symbols)
        List<MiningSymbol> updatedActiveSymbols = miningSymbolService.findActiveStockSymbols();
        assertThat(updatedActiveSymbols).isEmpty(); // All symbols should now have recent last_mined_date
    }

    @Test
    public void handleMineActiveStockSymbols_shouldHandleNoActiveSymbols() throws Exception {
        // Execute the listener when no active symbols exist
        mineActiveStockSymbolsListener.handleMineActiveStockSymbols(
                new HashMap<>(), channel, 1L, null);

        // Verify channel acknowledgment
        verify(channel).basicAck(1L, false);

        // Verify no symbols were processed (no publisher calls)
        verify(rabbitMqPublisher, org.mockito.Mockito.never()).mineStockDataBySymbols(any());
    }

}
