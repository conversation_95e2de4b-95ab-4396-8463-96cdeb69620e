package com.trading.financialindicatordaemon.amqp.listener;

import com.rabbitmq.client.Channel;
import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.amqp.publisher.RabbitMqPublisher;
import com.trading.financialindicatordaemon.mapper.MiningSymbol;
import com.trading.financialindicatordaemon.service.mining.MiningSymbolService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.jdbc.Sql;

import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

public class MineActiveSymbolsListenerTest extends BaseTest {

    @Autowired
    private MineActiveSymbolsListener mineActiveSymbolsListener;

    @Autowired
    private MiningSymbolService miningSymbolService;

    @MockBean
    private RabbitMqPublisher rabbitMqPublisher;

    @MockBean
    private Channel channel;

    @Test
    @Sql(scripts = "classpath:db/mining_symbols.sql")
    public void handleMineActiveSymbols_shouldProcessActiveCryptoSymbols() throws Exception {
        List<MiningSymbol> initialActiveSymbols = miningSymbolService.findActiveCryptoSymbols();
        assertThat(initialActiveSymbols).hasSize(2);
        assertThat(initialActiveSymbols.stream().map(MiningSymbol::getSymbol).toList())
                .contains("ETH", "SOL");

        mineActiveSymbolsListener.handleMineActiveSymbols(
                new HashMap<>(), channel, 1L, null);

        verify(channel).basicAck(1L, false);

        verify(rabbitMqPublisher).mineUsdDataBySymbols(any());
        verify(rabbitMqPublisher).mineBtcDataBySymbols(any());

        List<MiningSymbol> updatedActiveSymbols = miningSymbolService.findActiveCryptoSymbols();
        assertThat(updatedActiveSymbols).isEmpty();
    }

    @Test
    public void handleMineActiveSymbols_shouldHandleNoActiveCryptoSymbols() throws Exception {
        mineActiveSymbolsListener.handleMineActiveSymbols(
                new HashMap<>(), channel, 1L, null);

        verify(channel).basicAck(1L, false);

        verify(rabbitMqPublisher, org.mockito.Mockito.never()).mineUsdDataBySymbols(any());
        verify(rabbitMqPublisher, org.mockito.Mockito.never()).mineBtcDataBySymbols(any());
    }

}
