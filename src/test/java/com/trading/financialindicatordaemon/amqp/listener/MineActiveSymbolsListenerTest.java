package com.trading.financialindicatordaemon.amqp.listener;

import com.rabbitmq.client.Channel;
import com.trading.financialindicatordaemon.BaseTest;
import com.trading.financialindicatordaemon.amqp.publisher.RabbitMqPublisher;
import com.trading.financialindicatordaemon.mapper.MiningSymbol;
import com.trading.financialindicatordaemon.service.mining.MiningSymbolService;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.jdbc.Sql;

import java.util.HashMap;
import java.util.List;

import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;

public class MineActiveSymbolsListenerTest extends BaseTest {

    @Autowired
    private MineActiveSymbolsListener mineActiveSymbolsListener;

    @Autowired
    private MiningSymbolService miningSymbolService;

    @MockBean
    private RabbitMqPublisher rabbitMqPublisher;

    @MockBean
    private Channel channel;

    @Test
    @Sql(scripts = "classpath:db/mining_symbols.sql")
    public void handleMineActiveSymbols_shouldProcessActiveCryptoSymbols() throws Exception {
        // Verify initial state - should have 2 active symbols (ETH and SOL)
        List<MiningSymbol> initialActiveSymbols = miningSymbolService.findActiveCryptoSymbols();
        assertThat(initialActiveSymbols).hasSize(2);
        assertThat(initialActiveSymbols.stream().map(MiningSymbol::getSymbol).toList())
                .contains("ETH", "SOL");

        // Execute the listener
        mineActiveSymbolsListener.handleMineActiveSymbols(
                new HashMap<>(), channel, 1L, null);

        // Verify channel acknowledgment
        verify(channel).basicAck(1L, false);

        // Verify publisher was called with symbols for both USD and BTC
        verify(rabbitMqPublisher).mineUsdDataBySymbols(any());
        verify(rabbitMqPublisher).mineBtcDataBySymbols(any());

        // Verify that last mined dates were updated (should reduce active symbols)
        List<MiningSymbol> updatedActiveSymbols = miningSymbolService.findActiveCryptoSymbols();
        assertThat(updatedActiveSymbols).isEmpty(); // All symbols should now have recent last_mined_date
    }

    @Test
    public void handleMineActiveSymbols_shouldHandleNoActiveCryptoSymbols() throws Exception {
        // Execute the listener when no active symbols exist
        mineActiveSymbolsListener.handleMineActiveSymbols(
                new HashMap<>(), channel, 1L, null);

        // Verify channel acknowledgment
        verify(channel).basicAck(1L, false);

        // Verify no symbols were processed (no publisher calls)
        verify(rabbitMqPublisher, org.mockito.Mockito.never()).mineUsdDataBySymbols(any());
        verify(rabbitMqPublisher, org.mockito.Mockito.never()).mineBtcDataBySymbols(any());
    }

}
